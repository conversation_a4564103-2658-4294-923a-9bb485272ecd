<MithraRuntime>
    <connectionManager className="com.stpl.tech.attendance.config.MithraConnectionManager"/>

    <MithraObjectConfiguration className="com.stpl.tech.attendance.domain.EmpShiftMapping">
        <MithraObjectAttribute name="id" primaryKey="true"/>
        <MithraObjectAttribute name="empId"/>
        <MithraObjectAttribute name="shiftId"/>
        <MithraObjectAttribute name="expectedStartDate"/>
        <MithraObjectAttribute name="expectedEndDate"/>
        <MithraObjectAttribute name="status"/>
        <MithraObjectAttribute name="businessFrom"/>
        <MithraObjectAttribute name="businessTo"/>
        <MithraObjectAttribute name="processingFrom"/>
        <MithraObjectAttribute name="processingTo"/>
        <MithraObjectAttribute name="createdBy"/>
        <MithraObjectAttribute name="creationTime"/>
        <MithraObjectAttribute name="updatedBy"/>
        <MithraObjectAttribute name="updationTime"/>
    </MithraObjectConfiguration>

    <MithraObjectConfiguration className="com.stpl.tech.attendance.domain.ShiftCafeMapping">
        <MithraObjectAttribute name="id" primaryKey="true"/>
        <MithraObjectAttribute name="shiftId"/>
        <MithraObjectAttribute name="cafeId"/>
        <MithraObjectAttribute name="status"/>
        <MithraObjectAttribute name="businessFrom"/>
        <MithraObjectAttribute name="businessTo"/>
        <MithraObjectAttribute name="processingFrom"/>
        <MithraObjectAttribute name="processingTo"/>
        <MithraObjectAttribute name="createdBy"/>
        <MithraObjectAttribute name="creationTime"/>
        <MithraObjectAttribute name="updatedBy"/>
        <MithraObjectAttribute name="updationTime"/>
    </MithraObjectConfiguration>
</MithraRuntime>