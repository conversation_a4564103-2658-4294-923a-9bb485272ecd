package com.stpl.tech.attendance.domain;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.io.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.gs.fw.common.mithra.*;
import com.gs.fw.common.mithra.attribute.*;
import com.gs.fw.common.mithra.util.*;
import com.gs.fw.common.mithra.notification.*;
import com.gs.fw.common.mithra.notification.listener.*;
import com.gs.fw.common.mithra.list.cursor.Cursor;
import com.gs.fw.common.mithra.bulkloader.*;
import com.gs.fw.common.mithra.*;
import com.gs.fw.common.mithra.attribute.*;
import com.gs.fw.common.mithra.attribute.calculator.procedure.ObjectProcedure;
import com.gs.fw.common.mithra.behavior.txparticipation.*;
import com.gs.fw.common.mithra.cache.Cache;
import com.gs.fw.common.mithra.cache.bean.*;
import com.gs.fw.common.mithra.extractor.*;
import com.gs.fw.common.mithra.finder.*;
import com.gs.fw.common.mithra.finder.booleanop.*;
import com.gs.fw.common.mithra.finder.integer.*;
import com.gs.fw.common.mithra.finder.longop.*;
import com.gs.fw.common.mithra.finder.orderby.OrderBy;
import com.gs.fw.common.mithra.finder.string.*;
import com.gs.fw.common.mithra.extractor.NormalAndListValueSelector;
import com.gs.fw.common.mithra.list.NulledRelation;
import com.gs.fw.common.mithra.querycache.CachedQuery;
import com.gs.fw.common.mithra.querycache.QueryCache;
import com.gs.fw.common.mithra.portal.*;
import com.gs.fw.common.mithra.remote.*;
import com.gs.fw.common.mithra.transaction.MithraObjectPersister;
import com.gs.fw.common.mithra.util.TimestampPool;
import org.eclipse.collections.impl.map.mutable.UnifiedMap;
import java.io.Serializable;
/**
* This file was automatically generated using Mithra 17.0.1. Please do not modify it.
* Add custom logic to its subclass instead.
*/
public class EmpShiftMappingFinder
{
	private static final Timestamp businessDateInfinity = TimestampPool.getInstance().getOrAddToCache(java.sql.Timestamp.valueOf('9999-12-01 23:59:00.000'), true);
	private static final Timestamp businessDateDefault = TimestampPool.getInstance().getOrAddToCache(null, true);
	private static final Timestamp processingDateInfinity = TimestampPool.getInstance().getOrAddToCache(java.sql.Timestamp.valueOf('9999-12-01 23:59:00.000'), true);
	private static final Timestamp processingDateDefault = TimestampPool.getInstance().getOrAddToCache(java.sql.Timestamp.valueOf('9999-12-01 23:59:00.000'), true);
	private static final String IMPL_CLASS_NAME_WITH_SLASHES = "com/stpl/tech/attendance/domain/EmpShiftMapping";
	private static final String BUSINESS_CLASS_NAME_WITH_DOTS = "com.stpl.tech.attendance.domain.EmpShiftMapping";
	private static final FinderMethodMap finderMethodMap;
	private static boolean isFullCache;
	private static boolean isOffHeap;
	private static volatile MithraObjectPortal objectPortal = new UninitializedPortal("com.stpl.tech.attendance.domain.EmpShiftMapping");
	private static final EmpShiftMappingSingleFinder<EmpShiftMapping, Object, EmpShiftMapping> finder = new EmpShiftMappingSingleFinder<EmpShiftMapping, Object, EmpShiftMapping>();
	private static ConstantStringSet[] constantStringSets = new ConstantStringSet[0];
	private static ConstantIntSet[] constantIntSets = new ConstantIntSet[0];
	private static ConstantShortSet[] constantShortSets = new ConstantShortSet[0];
	static
	{
		finderMethodMap = new FinderMethodMap(EmpShiftMappingFinder.EmpShiftMappingRelatedFinder.class);
		finderMethodMap.addNormalAttributeName("id");
		finderMethodMap.addNormalAttributeName("shiftId");
		finderMethodMap.addNormalAttributeName("empId");
		finderMethodMap.addNormalAttributeName("expectedStartDate");
		finderMethodMap.addNormalAttributeName("expectedEndDate");
		finderMethodMap.addNormalAttributeName("status");
		finderMethodMap.addNormalAttributeName("createdBy");
		finderMethodMap.addNormalAttributeName("creationTime");
		finderMethodMap.addNormalAttributeName("updatedBy");
		finderMethodMap.addNormalAttributeName("updationTime");
		finderMethodMap.addNormalAttributeName("businessDateFrom");
		finderMethodMap.addNormalAttributeName("businessDateTo");
		finderMethodMap.addNormalAttributeName("processingDateFrom");
		finderMethodMap.addNormalAttributeName("processingDateTo");
		finderMethodMap.addNormalAttributeName("businessDate");
		finderMethodMap.addNormalAttributeName("processingDate");
	}

	public static Attribute[] allPersistentAttributes()
	{
		return finder.getPersistentAttributes();
	}

	public static List<RelatedFinder> allRelatedFinders()
	{
		return finder.getRelationshipFinders();
	}

	public static List<RelatedFinder> allDependentRelatedFinders()
	{
		return finder.getDependentRelationshipFinders();
	}

	public static ConstantStringSet zGetConstantStringSet(int index)
	{
		return constantStringSets[index];
	}

	public static ConstantIntSet zGetConstantIntSet(int index)
	{
		return constantIntSets[index];
	}

	public static ConstantShortSet zGetConstantShortSet(int index)
	{
		return constantShortSets[index];
	}

	public static SourceAttributeType getSourceAttributeType()
	{
		return null;
	}

	public static EmpShiftMapping findOne(com.gs.fw.finder.Operation operation)
	{
		return findOne(operation, false);
	}

	public static EmpShiftMapping findOneBypassCache(com.gs.fw.finder.Operation operation)
	{
		return findOne(operation, true);
	}

	public static EmpShiftMappingList findMany(com.gs.fw.finder.Operation operation)
	{
		return (EmpShiftMappingList) finder.findMany(operation);
	}

	public static EmpShiftMappingList findManyBypassCache(com.gs.fw.finder.Operation operation)
	{
		return (EmpShiftMappingList) finder.findManyBypassCache(operation);
	}

	private static EmpShiftMapping findOne(com.gs.fw.finder.Operation operation, boolean bypassCache)
	{
		List found = getMithraObjectPortal().find((Operation) operation, bypassCache);
		return (EmpShiftMapping) FinderUtils.findOne(found);
	}

	public static EmpShiftMapping findByPrimaryKey(int id, Timestamp businessDate, Timestamp processingDate)
	{
		return finder.findByPrimaryKey(id, businessDate, processingDate);
	}

	private static final RelationshipHashStrategy forPrimaryKey = new PrimaryKeyRhs();
	private static final class PrimaryKeyRhs implements RelationshipHashStrategy
	{
		public boolean equalsForRelationship(Object _srcObject, Object _srcData, Object _targetData, Timestamp _asOfDate0, Timestamp _asOfDate1)
		{
			I3O3L3 _bean = (I3O3L3) _srcData;
			EmpShiftMappingData _castedTargetData = (EmpShiftMappingData) _targetData;
			if (_bean.getI1AsInteger() == _castedTargetData.getId())
			{
				return EmpShiftMappingFinder.businessDate().dataMatches(_castedTargetData, _asOfDate0) && EmpShiftMappingFinder.processingDate().dataMatches(_castedTargetData, _asOfDate1);
			}

			return false;
		}

		public int computeHashCodeFromRelated(Object _srcObject, Object _srcData)
		{
			I3O3L3 _bean = (I3O3L3) _srcData;
			return HashUtil.hash(_bean.getI1AsInteger());
		}

		public int computeOffHeapHashCodeFromRelated(Object _srcObject, Object _srcData)
		{
			I3O3L3 _bean = (I3O3L3) _srcData;
			return HashUtil.hash(_bean.getI1AsInteger());
		}
	}

	public static EmpShiftMapping zFindOneForRelationship(Operation operation)
	{
		List found = getMithraObjectPortal().findAsCachedQuery(operation, null, false, true, 0).getResult();
		return (EmpShiftMapping) FinderUtils.findOne(found);
	}

	public static MithraObjectPortal getMithraObjectPortal()
	{
		return objectPortal.getInitializedPortal();
	}

	public static void clearQueryCache()
	{
		objectPortal.clearQueryCache();
	}

	public static void reloadCache()
	{
		objectPortal.reloadCache();
	}

	public static class EmpShiftMappingRelatedFinder<ParentOwnerType, ReturnType, ReturnListType extends List, OwnerType> extends AbstractRelatedFinder<EmpShiftMapping, ParentOwnerType, ReturnType, ReturnListType, OwnerType>
	{
		private List<RelatedFinder> relationshipFinders;
		private List<RelatedFinder> dependentRelationshipFinders;
		private IntegerAttribute<ParentOwnerType> id;
		private IntegerAttribute<ParentOwnerType> shiftId;
		private IntegerAttribute<ParentOwnerType> empId;
		private TimestampAttribute<ParentOwnerType> expectedStartDate;
		private TimestampAttribute<ParentOwnerType> expectedEndDate;
		private StringAttribute<ParentOwnerType> status;
		private StringAttribute<ParentOwnerType> createdBy;
		private TimestampAttribute<ParentOwnerType> creationTime;
		private StringAttribute<ParentOwnerType> updatedBy;
		private TimestampAttribute<ParentOwnerType> updationTime;
		private TimestampAttribute<ParentOwnerType> businessDateFrom;
		private TimestampAttribute<ParentOwnerType> businessDateTo;
		private TimestampAttribute<ParentOwnerType> processingDateFrom;
		private TimestampAttribute<ParentOwnerType> processingDateTo;
		private AsOfAttribute<ParentOwnerType> businessDate;
		private AsOfAttribute<ParentOwnerType> processingDate;
		private transient AsOfAttribute[] asOfAttributes;
		public synchronized AsOfAttribute[] getAsOfAttributes()
		{
			if (asOfAttributes == null)
			{
				asOfAttributes = new AsOfAttribute[2];
				asOfAttributes[0] = this.businessDate();
				asOfAttributes[1] = this.processingDate();
			}

			return this.asOfAttributes;
		}

		public EmpShiftMappingRelatedFinder()
		{
			super();
		}

		public EmpShiftMappingRelatedFinder(Mapper mapper)
		{
			super(mapper);
		}

		public String getFinderClassName()
		{
			return "com.stpl.tech.attendance.domain.EmpShiftMappingFinder";
		}

		public RelatedFinder getRelationshipFinderByName(String relationshipName)
		{
			return EmpShiftMappingFinder.finderMethodMap.getRelationshipFinderByName(relationshipName, this);
		}

		public Attribute getAttributeByName(String attributeName)
		{
			return EmpShiftMappingFinder.finderMethodMap.getAttributeByName(attributeName, this);
		}

		public com.gs.fw.common.mithra.extractor.Function getAttributeOrRelationshipSelector(String attributeName)
		{
			return EmpShiftMappingFinder.finderMethodMap.getAttributeOrRelationshipSelectorFunction(attributeName, this);
		}

		public Attribute[] getPersistentAttributes()
		{
			Attribute[] attributes = new Attribute[14];
			attributes[0] = this.id();
			attributes[1] = this.shiftId();
			attributes[2] = this.empId();
			attributes[3] = this.expectedStartDate();
			attributes[4] = this.expectedEndDate();
			attributes[5] = this.status();
			attributes[6] = this.createdBy();
			attributes[7] = this.creationTime();
			attributes[8] = this.updatedBy();
			attributes[9] = this.updationTime();
			attributes[10] = this.businessDateFrom();
			attributes[11] = this.businessDateTo();
			attributes[12] = this.processingDateFrom();
			attributes[13] = this.processingDateTo();
			return attributes;
		}

		public List<RelatedFinder> getRelationshipFinders()
		{
			if (relationshipFinders == null)
			{
				List<RelatedFinder> relatedFinders = new ArrayList<RelatedFinder>(0);
				relationshipFinders = Collections.unmodifiableList(relatedFinders);
			}

			return relationshipFinders;
		}

		public List<RelatedFinder> getDependentRelationshipFinders()
		{
			if (dependentRelationshipFinders == null)
			{
				List<RelatedFinder> dependentRelatedFinders = new ArrayList<RelatedFinder>(0);
				dependentRelationshipFinders = Collections.unmodifiableList(dependentRelatedFinders);
			}

			return dependentRelationshipFinders;
		}

		public EmpShiftMapping findOne(com.gs.fw.finder.Operation operation)
		{
			return EmpShiftMappingFinder.findOne(operation, false);
		}

		public EmpShiftMapping findOneBypassCache(com.gs.fw.finder.Operation operation)
		{
			return EmpShiftMappingFinder.findOne(operation, true);
		}

		public MithraList<? extends EmpShiftMapping> findMany(com.gs.fw.finder.Operation operation)
		{
			return new EmpShiftMappingList((Operation) operation);
		}

		public MithraList<? extends EmpShiftMapping> findManyBypassCache(com.gs.fw.finder.Operation operation)
		{
			EmpShiftMappingList result = (EmpShiftMappingList) this.findMany(operation);
			result.setBypassCache(true);
			return result;
		}

		public MithraList<? extends EmpShiftMapping> constructEmptyList()
		{
			return new EmpShiftMappingList();
		}

		public int getSerialVersionId()
		{
			return -315672043;
		}

		public boolean isPure()
		{
			return false;
		}

		public boolean isTemporary()
		{
			return false;
		}

		public int getHierarchyDepth()
		{
			return 0;
		}

		public IntegerAttribute<ParentOwnerType> id()
		{
			IntegerAttribute<ParentOwnerType> result = this.id;
			if (result == null)
			{
				result = mapper == null ? SingleColumnIntegerAttribute.generate("ID","","id",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,false,true,this,null,false,false,false,-1,-1,-1,false, false) :
					new MappedIntegerAttribute(EmpShiftMappingFinder.id(), this.mapper, this.zGetValueSelector());
				this.id = result;
			}

			return result;
		}

		public IntegerAttribute<ParentOwnerType> shiftId()
		{
			IntegerAttribute<ParentOwnerType> result = this.shiftId;
			if (result == null)
			{
				result = mapper == null ? SingleColumnIntegerAttribute.generate("SHIFT_ID","","shiftId",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,false,-1,-1,-1,false, false) :
					new MappedIntegerAttribute(EmpShiftMappingFinder.shiftId(), this.mapper, this.zGetValueSelector());
				this.shiftId = result;
			}

			return result;
		}

		public IntegerAttribute<ParentOwnerType> empId()
		{
			IntegerAttribute<ParentOwnerType> result = this.empId;
			if (result == null)
			{
				result = mapper == null ? SingleColumnIntegerAttribute.generate("EMP_ID","","empId",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,false,-1,-1,-1,false, false) :
					new MappedIntegerAttribute(EmpShiftMappingFinder.empId(), this.mapper, this.zGetValueSelector());
				this.empId = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> expectedStartDate()
		{
			TimestampAttribute<ParentOwnerType> result = this.expectedStartDate;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("EXPECTED_START_DATE","","expectedStartDate",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,false,null, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.expectedStartDate(), this.mapper, this.zGetValueSelector());
				this.expectedStartDate = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> expectedEndDate()
		{
			TimestampAttribute<ParentOwnerType> result = this.expectedEndDate;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("EXPECTED_END_DATE","","expectedEndDate",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,false,null, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.expectedEndDate(), this.mapper, this.zGetValueSelector());
				this.expectedEndDate = result;
			}

			return result;
		}

		public StringAttribute<ParentOwnerType> status()
		{
			StringAttribute<ParentOwnerType> result = this.status;
			if (result == null)
			{
				result = mapper == null ? SingleColumnStringAttribute.generate("STATUS","","status",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,-1,-1,-1,Integer.MAX_VALUE,true, false) :
					new MappedStringAttribute(EmpShiftMappingFinder.status(), this.mapper, this.zGetValueSelector());
				this.status = result;
			}

			return result;
		}

		public StringAttribute<ParentOwnerType> createdBy()
		{
			StringAttribute<ParentOwnerType> result = this.createdBy;
			if (result == null)
			{
				result = mapper == null ? SingleColumnStringAttribute.generate("CREATED_BY","","createdBy",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,-1,-1,-1,Integer.MAX_VALUE,true, false) :
					new MappedStringAttribute(EmpShiftMappingFinder.createdBy(), this.mapper, this.zGetValueSelector());
				this.createdBy = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> creationTime()
		{
			TimestampAttribute<ParentOwnerType> result = this.creationTime;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("CREATION_TIME","","creationTime",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,false,null, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.creationTime(), this.mapper, this.zGetValueSelector());
				this.creationTime = result;
			}

			return result;
		}

		public StringAttribute<ParentOwnerType> updatedBy()
		{
			StringAttribute<ParentOwnerType> result = this.updatedBy;
			if (result == null)
			{
				result = mapper == null ? SingleColumnStringAttribute.generate("UPDATED_BY","","updatedBy",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,-1,-1,-1,Integer.MAX_VALUE,true, false) :
					new MappedStringAttribute(EmpShiftMappingFinder.updatedBy(), this.mapper, this.zGetValueSelector());
				this.updatedBy = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> updationTime()
		{
			TimestampAttribute<ParentOwnerType> result = this.updationTime;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("UPDATION_TIME","","updationTime",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,true,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,false,null, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.updationTime(), this.mapper, this.zGetValueSelector());
				this.updationTime = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> businessDateFrom()
		{
			TimestampAttribute<ParentOwnerType> result = this.businessDateFrom;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("BUSINESS_FROM","","businessDateFrom",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,false,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,false,EmpShiftMappingFinder.businessDateInfinity, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.businessDateFrom(), this.mapper, this.zGetValueSelector());
				this.businessDateFrom = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> businessDateTo()
		{
			TimestampAttribute<ParentOwnerType> result = this.businessDateTo;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("BUSINESS_TO","","businessDateTo",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,false,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,true,EmpShiftMappingFinder.businessDateInfinity, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.businessDateTo(), this.mapper, this.zGetValueSelector());
				this.businessDateTo = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> processingDateFrom()
		{
			TimestampAttribute<ParentOwnerType> result = this.processingDateFrom;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("PROCESSING_FROM","","processingDateFrom",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,false,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,false,EmpShiftMappingFinder.processingDateInfinity, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.processingDateFrom(), this.mapper, this.zGetValueSelector());
				this.processingDateFrom = result;
			}

			return result;
		}

		public TimestampAttribute<ParentOwnerType> processingDateTo()
		{
			TimestampAttribute<ParentOwnerType> result = this.processingDateTo;
			if (result == null)
			{
				result = mapper == null ? SingleColumnTimestampAttribute.generate("PROCESSING_TO","","processingDateTo",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,false,true,this,null,false,false,-1,-1,-1,TimestampAttribute.CONVERT_NONE,false,true,EmpShiftMappingFinder.processingDateInfinity, (byte) 0, false) :
					new MappedTimestampAttribute(EmpShiftMappingFinder.processingDateTo(), this.mapper, this.zGetValueSelector());
				this.processingDateTo = result;
			}

			return result;
		}

		public AsOfAttribute<ParentOwnerType> businessDate()
		{
			AsOfAttribute<ParentOwnerType> result = this.businessDate;
			if (result == null)
			{
				result = mapper == null ? AsOfAttribute.generate("businessDate",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,false,true,this,null,false,false,this.businessDateFrom(),this.businessDateTo(),EmpShiftMappingFinder.businessDateInfinity,true,false,EmpShiftMappingFinder.businessDateDefault,false,false) :
					new MappedAsOfAttribute(EmpShiftMappingFinder.businessDate(), this.mapper, this.zGetValueSelector());
				this.businessDate = result;
			}

			return result;
		}

		public AsOfAttribute<ParentOwnerType> processingDate()
		{
			AsOfAttribute<ParentOwnerType> result = this.processingDate;
			if (result == null)
			{
				result = mapper == null ? AsOfAttribute.generate("processingDate",BUSINESS_CLASS_NAME_WITH_DOTS,IMPL_CLASS_NAME_WITH_SLASHES,false,true,this,null,false,false,this.processingDateFrom(),this.processingDateTo(),EmpShiftMappingFinder.processingDateInfinity,false,false,EmpShiftMappingFinder.processingDateDefault,true,false) :
					new MappedAsOfAttribute(EmpShiftMappingFinder.processingDate(), this.mapper, this.zGetValueSelector());
				this.processingDate = result;
			}

			return result;
		}

		public Attribute getSourceAttribute()
		{
			return null;
		}

		private Mapper combineWithMapperIfExists(Mapper newMapper)
		{
			if (this.mapper != null)
			{
				return new LinkedMapper(this.mapper, newMapper);
			}

			return newMapper;
		}

		public Attribute[] getPrimaryKeyAttributes()
		{
			return EmpShiftMappingFinder.getPrimaryKeyAttributes();
		}

		public VersionAttribute getVersionAttribute()
		{
			return null;
		}

		public MithraObjectPortal getMithraObjectPortal()
		{
			return EmpShiftMappingFinder.getMithraObjectPortal();
		}
	}

	public static class EmpShiftMappingCollectionFinder<ParentOwnerType, ReturnType extends List, OwnerType> extends EmpShiftMappingRelatedFinder<ParentOwnerType, ReturnType, EmpShiftMappingList, OwnerType>
	{
		public EmpShiftMappingCollectionFinder(Mapper mapper)
		{
			super(mapper);
		}
	}

	public static abstract class EmpShiftMappingCollectionFinderForRelatedClasses<ParentOwnerType, ReturnType extends List, OwnerType>
	extends EmpShiftMappingCollectionFinder<ParentOwnerType, ReturnType, OwnerType>
	implements DeepRelationshipAttribute<ParentOwnerType, ReturnType>
	{
		public EmpShiftMappingCollectionFinderForRelatedClasses(Mapper mapper)
		{
			super(mapper);
		}

		protected NormalAndListValueSelector zGetValueSelector()
		{
			return this;
		}
	}

	public static class EmpShiftMappingSingleFinder<ParentOwnerType, ReturnType, OwnerType> extends EmpShiftMappingRelatedFinder<ParentOwnerType, ReturnType, EmpShiftMappingList, OwnerType>
	implements ToOneFinder
	{
		public EmpShiftMappingSingleFinder(Mapper mapper)
		{
			super(mapper);
		}

		public EmpShiftMappingSingleFinder()
		{
			super(null);
		}

		public Operation eq(EmpShiftMapping other)
		{
			return this.id().eq(other.getId())
			;
		}
		// this implementation uses private API. Do NOT copy to application code. Application code must use normal operations for lookups.
		public EmpShiftMapping findByPrimaryKey(int id, Timestamp businessDate, Timestamp processingDate)
		{
			EmpShiftMapping _result = null;
			Operation _op = null;
			Object _related = null;
			{
				I3O3L3 _bean = I3O3L3.POOL.getOrConstruct();
				_bean.setI1AsInteger(id);
				MithraObjectPortal _portal = this.getMithraObjectPortal();
				_related = _portal.getAsOneFromCacheForFind(_bean, _bean, forPrimaryKey, businessDate, processingDate);
				_bean.release();
			}

			if (!(_related instanceof NulledRelation)) _result = (EmpShiftMapping) _related;
			if (_related == null)
			{
				_op = this.id().eq(id).and(this.businessDate().eq(businessDate)).and(this.processingDate().eq(processingDate));
			}

			if (_op != null)
			{
				_result = this.findOne(_op);
			}

			return _result;
		}
	}

	public static abstract class EmpShiftMappingSingleFinderForRelatedClasses<ParentOwnerType, ReturnType, OwnerType> extends EmpShiftMappingSingleFinder<ParentOwnerType, ReturnType, OwnerType> implements DeepRelationshipAttribute<ParentOwnerType, ReturnType>
	{
		public EmpShiftMappingSingleFinderForRelatedClasses(Mapper mapper)
		{
			super(mapper);
		}

		protected NormalAndListValueSelector zGetValueSelector()
		{
			return this;
		}
	}

	/** maps to EMP_SHIFT_MAPPING.ID **/
	public static IntegerAttribute<EmpShiftMapping> id()
	{
		return finder.id();
	}

	/** maps to EMP_SHIFT_MAPPING.SHIFT_ID **/
	public static IntegerAttribute<EmpShiftMapping> shiftId()
	{
		return finder.shiftId();
	}

	/** maps to EMP_SHIFT_MAPPING.EMP_ID **/
	public static IntegerAttribute<EmpShiftMapping> empId()
	{
		return finder.empId();
	}

	/** maps to EMP_SHIFT_MAPPING.EXPECTED_START_DATE **/
	public static TimestampAttribute<EmpShiftMapping> expectedStartDate()
	{
		return finder.expectedStartDate();
	}

	/** maps to EMP_SHIFT_MAPPING.EXPECTED_END_DATE **/
	public static TimestampAttribute<EmpShiftMapping> expectedEndDate()
	{
		return finder.expectedEndDate();
	}

	/** maps to EMP_SHIFT_MAPPING.STATUS **/
	public static StringAttribute<EmpShiftMapping> status()
	{
		return finder.status();
	}

	/** maps to EMP_SHIFT_MAPPING.CREATED_BY **/
	public static StringAttribute<EmpShiftMapping> createdBy()
	{
		return finder.createdBy();
	}

	/** maps to EMP_SHIFT_MAPPING.CREATION_TIME **/
	public static TimestampAttribute<EmpShiftMapping> creationTime()
	{
		return finder.creationTime();
	}

	/** maps to EMP_SHIFT_MAPPING.UPDATED_BY **/
	public static StringAttribute<EmpShiftMapping> updatedBy()
	{
		return finder.updatedBy();
	}

	/** maps to EMP_SHIFT_MAPPING.UPDATION_TIME **/
	public static TimestampAttribute<EmpShiftMapping> updationTime()
	{
		return finder.updationTime();
	}

	/** maps to EMP_SHIFT_MAPPING.BUSINESS_FROM **/
	public static TimestampAttribute<EmpShiftMapping> businessDateFrom()
	{
		return finder.businessDateFrom();
	}

	/** maps to EMP_SHIFT_MAPPING.BUSINESS_TO **/
	public static TimestampAttribute<EmpShiftMapping> businessDateTo()
	{
		return finder.businessDateTo();
	}

	/** maps to EMP_SHIFT_MAPPING.PROCESSING_FROM **/
	public static TimestampAttribute<EmpShiftMapping> processingDateFrom()
	{
		return finder.processingDateFrom();
	}

	/** maps to EMP_SHIFT_MAPPING.PROCESSING_TO **/
	public static TimestampAttribute<EmpShiftMapping> processingDateTo()
	{
		return finder.processingDateTo();
	}

	public static AsOfAttribute<EmpShiftMapping> businessDate()
	{
		return finder.businessDate();
	}

	public static AsOfAttribute<EmpShiftMapping> processingDate()
	{
		return finder.processingDate();
	}

	public static Operation eq(EmpShiftMapping other)
	{
		return finder.eq(other);
	}

	public static Operation all()
	{
		return new All(id());
	}

	public static EmpShiftMappingSingleFinder<EmpShiftMapping, Object, EmpShiftMapping> getFinderInstance()
	{
		return finder;
	}

	public static Attribute[] getPrimaryKeyAttributes()
	{
		return new Attribute[] 
		{
			id()
		}

		;
	}

	public static Attribute[] getImmutableAttributes()
	{
		return new Attribute[] 
		{
			id()
			, id()
		}

		;
	}

	public static AsOfAttribute[] getAsOfAttributes()
	{
		return new AsOfAttribute[] 
		{
			businessDate()
			, processingDate()
		}

		;
	}

	protected static void initializeIndicies(Cache cache)
	{
	}

	protected static void initializePortal(MithraObjectDeserializer objectFactory, Cache cache,
		MithraConfigurationManager.Config config)
	{
		initializeIndicies(cache);
		isFullCache = cache.isFullCache();
		isOffHeap = cache.isOffHeap();
		MithraObjectPortal portal;
		if (config.isParticipatingInTx())
		{
			portal = new MithraReadOnlyPortal(objectFactory, cache, getFinderInstance(),
				config.getRelationshipCacheSize(), config.getMinQueriesToKeep(), null,
				null, null, 0,
				(MithraObjectReader) objectFactory);
		}
		else
		{
			portal = new MithraReadOnlyPortal(objectFactory, cache, getFinderInstance(),
				config.getRelationshipCacheSize(), config.getMinQueriesToKeep(), null,
				null, null, 0,
				(MithraObjectReader) objectFactory);
		}

		portal.setIndependent(true);
		config.initializePortal(portal);
		objectPortal.destroy();
		objectPortal = portal;
	}

	protected static void initializeClientPortal(MithraObjectDeserializer objectFactory, Cache cache,
		MithraConfigurationManager.Config config)
	{
		initializeIndicies(cache);
		isFullCache = cache.isFullCache();
		isOffHeap = cache.isOffHeap();
		MithraObjectPortal portal;
		if (config.isParticipatingInTx())
		{
			portal = new MithraReadOnlyPortal(objectFactory, cache, getFinderInstance(),
				config.getRelationshipCacheSize(), config.getMinQueriesToKeep(),
				null, null,
				null, 0,
				new RemoteMithraObjectPersister(config.getRemoteMithraService(), getFinderInstance(), true));
		}
		else
		{
			portal = new MithraReadOnlyPortal(objectFactory, cache, getFinderInstance(),
				config.getRelationshipCacheSize(), config.getMinQueriesToKeep(),
				null, null,
				null, 0,
				new RemoteMithraObjectPersister(config.getRemoteMithraService(), getFinderInstance(), true));
		}

		portal.setIndependent(true);
		config.initializePortal(portal);
		objectPortal.destroy();
		objectPortal = portal;
	}

	public static boolean isFullCache()
	{
		return isFullCache;
	}

	public static boolean isOffHeap()
	{
		return isOffHeap;
	}

	public static Attribute getAttributeByName(String attributeName)
	{
		return finder.getAttributeByName(attributeName);
	}

	public static com.gs.fw.common.mithra.extractor.Function getAttributeOrRelationshipSelector(String attributeName)
	{
		return finder.getAttributeOrRelationshipSelector(attributeName);
	}

	public static RelatedFinder getRelatedFinderByName(String relationshipName)
	{
		return finder.getRelationshipFinderByName(relationshipName);
	}

	public static DoubleAttribute[] zGetDoubleAttributes()
	{
		DoubleAttribute[] result = new DoubleAttribute[0];
		return result;
	}

	public static BigDecimalAttribute[] zGetBigDecimalAttributes()
	{
		BigDecimalAttribute[] result = new BigDecimalAttribute[0];
		return result;
	}

	public static void zResetPortal()
	{
		objectPortal.destroy();
		objectPortal = new UninitializedPortal("com.stpl.tech.attendance.domain.EmpShiftMapping");
		isFullCache = false;
		isOffHeap = false;
	}

	public static void setTransactionModeFullTransactionParticipation(MithraTransaction tx)
	{
		tx.setTxParticipationMode(objectPortal, FullTransactionalParticipationMode.getInstance());
	}

	public static void registerForNotification(MithraApplicationClassLevelNotificationListener listener)
	{
		getMithraObjectPortal().registerForApplicationClassLevelNotification(listener);
	}
}
