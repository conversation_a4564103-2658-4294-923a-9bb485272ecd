package com.stpl.tech.attendance.domain;
import java.sql.Timestamp;
public class EmpShiftMapping extends EmpShiftMappingAbstract
{
	public EmpShiftMapping(Timestamp businessDate
	, Timestamp processingDate
	)
	{
		super(businessDate
		,processingDate
		);
		// You must not modify this constructor. <PERSON><PERSON><PERSON> calls this internally.
		// You can call this constructor. You can also add new constructors.
	}

	public EmpShiftMapping(Timestamp businessDate)
	{
		super(businessDate);
	}
}
