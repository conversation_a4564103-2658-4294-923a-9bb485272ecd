package com.stpl.tech.attendance.service.RosteringService.impl;

import com.gs.fw.common.mithra.MithraManager;
import com.gs.fw.common.mithra.MithraTransaction;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.service.RosteringService.ReladomoEmpShiftService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

// Import generated Reladomo classes
import com.stpl.tech.attendance.domain.EmpShiftMapping;
import com.stpl.tech.attendance.domain.EmpShiftMappingList;

/**
 * Reladomo-based service for employee shift operations with bitemporal support
 * This service uses generated Reladomo classes for high-performance bitemporal operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReladomoEmpShiftServiceImpl implements ReladomoEmpShiftService {

    @Override
    public EmpShiftUpdateResponseDTO updateEmpShifts(EmpShiftUpdateRequestDTO request) {
        log.info("Updating employee shift mappings with Reladomo bitemporal: empIds={}, shiftIds={}, businessFrom={}, businessTo={}",
                request.getEmpIds(), request.getShiftIds(), request.getBusinessFrom(), request.getBusinessTo());

        // Validate request
        validateShiftUpdateRequest(request);

        MithraTransaction tx = MithraManager.getInstance().startOrContinueTransaction();
        try {
            // Perform bitemporal update operations
            UpdateResult result = performReladomoBulkUpdate(request, tx);

            tx.commit();
            log.info("Successfully updated {} employee shift mappings using bitemporal operations", result.getTotalUpdated());

            return EmpShiftUpdateResponseDTO.builder()
                .success(true)
                .message("Shifts updated successfully using bitemporal operations")
                .updatedShifts(request.getShiftIds())
                .updatedEmployees(request.getEmpIds())
                .totalUpdatedMappings(result.getTotalUpdated())
                .build();

        } catch (Exception e) {
            log.error("Error updating employee shift mappings with bitemporal operations", e);
            throw new RuntimeException("Failed to update employee shift mappings", e);
        }
    }

    /**
     * Perform bulk update using Reladomo with bitemporal pattern
     */
    private UpdateResult performReladomoBulkUpdate(EmpShiftUpdateRequestDTO request, MithraTransaction tx) {
        log.debug("Performing Reladomo bitemporal bulk update for {} employees and {} shifts",
                request.getEmpIds().size(), request.getShiftIds().size());

        int totalUpdated = 0;
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime businessFrom = request.getBusinessFrom();
        LocalDateTime businessTo = request.getBusinessTo();
        LocalDateTime processingFrom = currentTime;
        LocalDateTime processingTo = LocalDateTime.of(9999, 12, 31, 23, 59, 59); // Infinity

        // For each employee-shift combination
        for (Integer empId : request.getEmpIds()) {
            for (Integer shiftId : request.getShiftIds()) {
                // Terminate existing active mappings for this employee in the business date range
                terminateExistingMappings(empId, businessFrom, processingFrom);

                // Create new bitemporal mapping using Reladomo
                EmpShiftMapping newMapping = createReladomoBitemporalMapping(
                    empId, shiftId, request, businessFrom, businessTo,
                    processingFrom, processingTo, currentTime);

                // In Reladomo, objects are automatically inserted when created within a transaction
                // The transaction context handles the persistence
                totalUpdated++;

                log.debug("Created Reladomo bitemporal mapping: empId={}, shiftId={}, businessFrom={}, businessTo={}",
                         empId, shiftId, businessFrom, businessTo);
            }
        }

        return new UpdateResult(totalUpdated);
    }

    @Override
    public List<EmpShiftMappingDTO> findCurrentShiftsByEmpId(Integer empId, LocalDateTime businessDate) {
        log.debug("Finding current shifts for employee: {} at business date: {}", empId, businessDate);

        LocalDateTime businessTimestamp = businessDate != null ? businessDate : LocalDateTime.now();
        LocalDateTime currentTime = LocalDateTime.now();

        // Use Reladomo for bitemporal queries
        EmpShiftMappingList mappings = EmpShiftMappingFinder.findMany(
            EmpShiftMappingFinder.empId().eq(empId)
            .and(EmpShiftMappingFinder.status().eq(RosteringConstants.SHIFT_STATUS_ACTIVE))
            .and(EmpShiftMappingFinder.businessDate().eq(toTimestamp(businessTimestamp)))
            .and(EmpShiftMappingFinder.processingDate().eq(toTimestamp(currentTime)))
        );

        return mappings.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    public List<EmpShiftMappingDTO> findShiftHistoryByEmpId(Integer empId) {
        log.debug("Finding shift history for employee: {}", empId);

        LocalDateTime currentTime = LocalDateTime.now();

        // Use Reladomo for processing time queries
        EmpShiftMappingList mappings = EmpShiftMappingFinder.findMany(
            EmpShiftMappingFinder.empId().eq(empId)
            .and(EmpShiftMappingFinder.processingDate().eq(toTimestamp(currentTime)))
        );

        // Sort by business from date to get chronological order
        mappings.sort(EmpShiftMappingFinder.businessDateFrom().ascendingOrderBy());

        return mappings.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }



    /**
     * Validate the update request
     */
    private void validateShiftUpdateRequest(EmpShiftUpdateRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        if (request.getEmpIds() == null || request.getEmpIds().isEmpty()) {
            throw new IllegalArgumentException("Employee IDs are required");
        }

        if (request.getShiftIds() == null || request.getShiftIds().isEmpty()) {
            throw new IllegalArgumentException("Shift IDs are required");
        }

        if (request.getBusinessFrom() == null) {
            throw new IllegalArgumentException("Business from date is required");
        }

        if (request.getBusinessTo() == null) {
            throw new IllegalArgumentException("Business to date is required");
        }

        if (request.getBusinessFrom().isAfter(request.getBusinessTo())) {
            throw new IllegalArgumentException("Business from date cannot be after business to date");
        }

        // Validate that all empIds and shiftIds are positive
        if (request.getEmpIds().stream().anyMatch(id -> id == null || id <= 0)) {
            throw new IllegalArgumentException("All employee IDs must be positive integers");
        }

        if (request.getShiftIds().stream().anyMatch(id -> id == null || id <= 0)) {
            throw new IllegalArgumentException("All shift IDs must be positive integers");
        }

        log.debug("Update request validation passed for {} employees and {} shifts",
                 request.getEmpIds().size(), request.getShiftIds().size());
    }

    /**
     * Helper class to hold update results
     */
    private static class UpdateResult {
        private final int totalUpdated;

        public UpdateResult(int totalUpdated) {
            this.totalUpdated = totalUpdated;
        }

        public int getTotalUpdated() {
            return totalUpdated;
        }
    }

    /**
     * Convert Reladomo EmpShiftMapping to DTO
     */
    private EmpShiftMappingDTO convertToDTO(EmpShiftMapping mapping) {
        if (mapping == null) {
            return null;
        }

        return EmpShiftMappingDTO.builder()
            .id(mapping.getId())
            .shiftId(mapping.getShiftId())
            .empId(mapping.getEmpId())
            .expectedStartDate(mapping.getExpectedStartDate() != null ? mapping.getExpectedStartDate().toLocalDateTime() : null)
            .expectedEndDate(mapping.getExpectedEndDate() != null ? mapping.getExpectedEndDate().toLocalDateTime() : null)
            .processingFrom(mapping.getProcessingDateFrom() != null ? mapping.getProcessingDateFrom().toLocalDateTime() : null)
            .processingTo(mapping.getProcessingDateTo() != null ? mapping.getProcessingDateTo().toLocalDateTime() : null)
            .businessFrom(mapping.getBusinessDateFrom() != null ? mapping.getBusinessDateFrom().toLocalDateTime() : null)
            .businessTo(mapping.getBusinessDateTo() != null ? mapping.getBusinessDateTo().toLocalDateTime() : null)
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime() != null ? mapping.getCreationTime().toLocalDateTime() : null)
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime() != null ? mapping.getUpdationTime().toLocalDateTime() : null)
            .build();
    }

    private Timestamp toTimestamp(LocalDateTime localDateTime) {
        return localDateTime != null ? Timestamp.valueOf(localDateTime) : null;
    }



    /**
     * Terminate existing active mappings for an employee using Reladomo
     */
    private void terminateExistingMappings(Integer empId, LocalDateTime businessFrom, LocalDateTime processingFrom) {
        EmpShiftMappingList existingMappings = EmpShiftMappingFinder.findMany(
            EmpShiftMappingFinder.empId().eq(empId)
            .and(EmpShiftMappingFinder.status().eq("ACTIVE"))
        );

        for (EmpShiftMapping existing : existingMappings) {
            // Check if this mapping overlaps with the new business date range
            if (existing.getBusinessDateTo() == null || 
                existing.getBusinessDateTo().toLocalDateTime().isAfter(businessFrom)) {
                // Terminate the existing mapping by setting processing end date
                existing.setProcessingDateTo(toTimestamp(processingFrom));
                // In Reladomo, changes are automatically persisted when the transaction is committed

                log.debug("Terminated existing Reladomo mapping for empId: {}, mappingId: {}", empId, existing.getId());
            }
        }
    }

    /**
     * Create a new bitemporal mapping using Reladomo
     */
    private EmpShiftMapping createReladomoBitemporalMapping(Integer empId, Integer shiftId,
                                                           EmpShiftUpdateRequestDTO request,
                                                           LocalDateTime businessFrom, LocalDateTime businessTo,
                                                           LocalDateTime processingFrom, LocalDateTime processingTo,
                                                           LocalDateTime currentTime) {
        EmpShiftMapping newMapping = new EmpShiftMapping(
            toTimestamp(businessFrom), 
            toTimestamp(processingFrom)
        );

        newMapping.setEmpId(empId);
        newMapping.setShiftId(shiftId);
        newMapping.setExpectedStartDate(request.getExpectedArrivalTime() != null ? 
            toTimestamp(request.getExpectedArrivalTime()) : null);
        newMapping.setExpectedEndDate(null); // Will be set based on shift timing
        newMapping.setBusinessDateFrom(toTimestamp(businessFrom));
        newMapping.setBusinessDateTo(toTimestamp(businessTo));
        newMapping.setProcessingDateFrom(toTimestamp(processingFrom));
        newMapping.setProcessingDateTo(toTimestamp(processingTo));
        newMapping.setStatus("ACTIVE");
        newMapping.setCreatedBy(request.getUpdatedBy());
        newMapping.setUpdatedBy(request.getUpdatedBy());
        newMapping.setCreationTime(toTimestamp(currentTime));
        newMapping.setUpdationTime(toTimestamp(currentTime));

        return newMapping;
    }
}