package com.stpl.tech.attendance.domain;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.io.*;

import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.gs.fw.common.mithra.*;
import com.gs.fw.common.mithra.attribute.*;
import com.gs.fw.common.mithra.util.*;
import com.gs.fw.common.mithra.notification.*;
import com.gs.fw.common.mithra.notification.listener.*;
import com.gs.fw.common.mithra.list.cursor.Cursor;
import com.gs.fw.common.mithra.bulkloader.*;
import java.util.*;
import java.sql.*;
import com.gs.fw.common.mithra.*;
import com.gs.fw.common.mithra.attribute.AsOfAttribute;
import com.gs.fw.common.mithra.cache.*;
import com.gs.fw.common.mithra.cache.offheap.*;
import com.gs.fw.common.mithra.connectionmanager.*;
import com.gs.fw.common.mithra.database.*;
import com.gs.fw.common.mithra.finder.*;
import com.gs.fw.common.mithra.finder.orderby.OrderBy;
import com.gs.fw.common.mithra.finder.integer.IntegerResultSetParser;
import com.gs.fw.common.mithra.databasetype.*;
import com.gs.fw.common.mithra.querycache.CachedQuery;
import com.gs.fw.common.mithra.finder.asofop.AsOfOperation;
import com.gs.fw.common.mithra.remote.RemoteMithraService;
/**
* This file was automatically generated using Mithra 17.0.1. Please do not modify it.
* Add custom logic to its subclass instead.
*/
public abstract class EmpShiftMappingDatabaseObjectAbstract extends MithraAbstractDatedDatabaseObject implements MithraDatabaseObject, MithraDatedObjectFactory
{
	private SourcelessConnectionManager connectionManager;
	private SchemaManager schemaManager;
	private TablePartitionManager tablePartitionManager;
	private static final String COL_LIST_WITHOUT_PK = "SHIFT_ID,EMP_ID,EXPECTED_START_DATE,EXPECTED_END_DATE,STATUS,CREATED_BY,CREATION_TIME,UPDATED_BY,UPDATION_TIME,BUSINESS_FROM,BUSINESS_TO,PROCESSING_FROM,PROCESSING_TO";
	private static final String COL_LIST_WITHOUT_PK_WITH_ALIAS = "t0.SHIFT_ID,t0.EMP_ID,t0.EXPECTED_START_DATE,t0.EXPECTED_END_DATE,t0.STATUS,t0.CREATED_BY,t0.CREATION_TIME,t0.UPDATED_BY,t0.UPDATION_TIME,t0.BUSINESS_FROM,t0.BUSINESS_TO,t0.PROCESSING_FROM,t0.PROCESSING_TO";
	private static final String PK_WITH_ALIAS = "t0.ID = ?";
	private static final String PK_INDEX_COLS = "ID";
	protected EmpShiftMappingDatabaseObjectAbstract()
	{
		super("EmpShiftMapping", "com.stpl.tech.attendance.domain.EmpShiftMappingFinder",
			14, 14,
			COL_LIST_WITHOUT_PK, COL_LIST_WITHOUT_PK_WITH_ALIAS,
			false, false, false,
			PK_WITH_ALIAS,
			PK_INDEX_COLS);
	}

	public MithraObjectPortal getMithraObjectPortal()
	{
		return EmpShiftMappingFinder.getMithraObjectPortal();
	}

	public RelatedFinder getFinder()
	{
		return EmpShiftMappingFinder.getFinderInstance();
	}

	public static EmpShiftMappingData allocateOnHeapData()
	{
		return new EmpShiftMappingData();
	}

	public static EmpShiftMappingData allocateOffHeapData()
	{
		throw new RuntimeException("no off heap implementation");
	}

	public MithraDataObject deserializeFullData(ObjectInput in) throws IOException, ClassNotFoundException
	{
		MithraDataObject data = new EmpShiftMappingData();
		data.zDeserializeFullData(in);
		return data;
	}

	public void deserializeAsOfAttributes(ObjectInput in, Timestamp[] asof) throws IOException, ClassNotFoundException
	{
		asof[0] = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000")), EmpShiftMappingFinder.isFullCache());
		asof[1] = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, java.sql.Timestamp.valueOf("9999-12-01 23:59:00.000")), EmpShiftMappingFinder.isFullCache());
	}

	public MithraObject deserializeForRefresh(ObjectInput in) throws IOException, ClassNotFoundException
	{
		EmpShiftMappingData data = new EmpShiftMappingData();
		data.zDeserializePrimaryKey(in);
		Timestamp[] asof = new Timestamp[2];
		asof[0] = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME)), EmpShiftMappingFinder.isFullCache());
		asof[1] = TimestampPool.getInstance().getOrAddToCache(MithraTimestamp.readTimezoneInsensitiveTimestampWithInfinity(in, java.sql.Timestamp.valueOf(RosteringConstants.INFINITY_TIME)), EmpShiftMappingFinder.isFullCache());
		return this.createObject(data, asof);
	}

	public Cache instantiateFullCache(MithraConfigurationManager.Config config)
	{
		Cache result;
		result = new FullDatedCache(EmpShiftMappingFinder.getPrimaryKeyAttributes(), EmpShiftMappingFinder.getAsOfAttributes(), this, EmpShiftMappingFinder.getImmutableAttributes());
		initPortal(result, config);
		return result;
	}

	public Cache instantiatePartialCache(MithraConfigurationManager.Config config)
	{
		Cache result;
		result = new PartialDatedCache(EmpShiftMappingFinder.getPrimaryKeyAttributes(), EmpShiftMappingFinder.getAsOfAttributes(), this, EmpShiftMappingFinder.getImmutableAttributes(), config.getCacheTimeToLive(), config.getRelationshipCacheTimeToLive());
		initPortal(result, config);
		return result;
	}

	private void initPortal(Cache cache, MithraConfigurationManager.Config config)
	{
		if (config.isThreeTierClient())
		{
			EmpShiftMappingFinder.initializeClientPortal(this, cache, config);
		}
		else
		{
			EmpShiftMappingFinder.initializePortal(this, cache, config);
		}
	}

	public List getSimulatedSequenceInitValues()
	{
		return null;
	}

	public Object getSourceAttributeValueForSelectedObjectGeneric(SqlQuery query, int queryNumber)
	{
		return null;
	}

	public Object getSourceAttributeValueFromObjectGeneric(MithraDataObject object)
	{
		return null;
	}

	public Object getSourceAttributeValueGeneric(SqlQuery query, MapperStackImpl mapperStack, int queryNumber)
	{
		return null;
	}

	public String getDatabaseIdentifierGenericSource (Object source)
	{
		return connectionManager.getDatabaseIdentifier();
	}

	public DatabaseType getDatabaseTypeGenericSource(Object source)
	{
		return connectionManager.getDatabaseType();
	}

	public TimeZone getDatabaseTimeZoneGenericSource(Object source)
	{
		return getDatabaseTimeZone();
	}

	public Connection getConnectionGenericSource(Object source)
	{
		return connectionManagerWrapper.getConnection();
	}

	public BulkLoader createBulkLoaderGenericSource(Object source) throws BulkLoaderException 
	{
		return connectionManager.createBulkLoader();
	}

	public MithraDataObject inflateDataGenericSource(ResultSet rs, Object source, DatabaseType dt)
	throws SQLException 
	{
		return inflateEmpShiftMappingData(rs, dt);
	}

	public void inflateNonPkDataGenericSource(MithraDataObject data, ResultSet rs, Object source, DatabaseType dt)
	throws SQLException 
	{
		inflateNonPkEmpShiftMappingData(1, (EmpShiftMappingData) data, rs, dt);
	}

	public MithraDataObject inflatePkDataGenericSource(ResultSet rs, Object source, DatabaseType dt)
	throws SQLException 
	{
		return inflateEmpShiftMappingPkData(rs, dt);
	}

	public String getSchemaGenericSource(Object source)
	{
		if (this.schemaManager != null)
		{
			return this.schemaManager.getSchema(this.getDefaultSchema());
		}

		return this.getDefaultSchema();
	}

	public String getTableNameGenericSource(Object source) throws MithraDatabaseException
	{
		return getEmpShiftMappingTableName();
	}

	public String getEmpShiftMappingTableName() throws MithraDatabaseException
	{
		if (this.tablePartitionManager != null)
		{
			return this.tablePartitionManager.getTableName(this.getDefaultTableName());
		}

		return this.getDefaultTableName();
	}

	public void setPrimaryKeyAttributes(PreparedStatement stm, int pos, MithraDataObject dataObj,
		TimeZone databaseTimeZone, DatabaseType dt) throws SQLException
	{
		EmpShiftMappingData data = (EmpShiftMappingData)dataObj;
		TimeZone conversionTimeZone = null;
		stm.setInt(pos++, data.getId());
		conversionTimeZone = MithraTimestamp.DefaultTimeZone;
		if (data.getBusinessDateTo().getTime() == EmpShiftMappingFinder.businessDate().getInfinityDate().getTime())
		{
			conversionTimeZone = MithraTimestamp.DefaultTimeZone;
		}

		dt.setTimestamp(stm, pos, data.getBusinessDateTo(), false, conversionTimeZone);
		pos++;
		conversionTimeZone = MithraTimestamp.DefaultTimeZone;
		if (data.getProcessingDateTo().getTime() == EmpShiftMappingFinder.processingDate().getInfinityDate().getTime())
		{
			conversionTimeZone = MithraTimestamp.DefaultTimeZone;
		}

		dt.setTimestamp(stm, pos, data.getProcessingDateTo(), false, conversionTimeZone);
		pos++;
	}

	public int setPrimaryKeyAttributesWithoutOptimistic(PreparedStatement stm, int pos, MithraDataObject dataObj,
		TimeZone databaseTimeZone, DatabaseType dt) throws SQLException
	{
		this.setPrimaryKeyAttributes(stm, pos, dataObj, databaseTimeZone, dt);
		return -1;
	}

	public String getPrimaryKeyWhereSql()
	{
		return "ID = ?";
	}

	public String getPrimaryKeyWhereSqlWithNullableAttribute(MithraDataObject dataObj)
	{
		return "";
	}

	public String getPrimaryKeyWhereSqlWithNullableAttributeWithDefaultAlias(MithraDataObject dataObj)
	{
		return "";
	}

	public String getColumnListWithPk(String databaseAlias)
	{
		if (databaseAlias.equals(SqlQuery.DEFAULT_DATABASE_ALIAS))
		{
			return "t0.ID,t0.SHIFT_ID,t0.EMP_ID,t0.EXPECTED_START_DATE,t0.EXPECTED_END_DATE,t0.STATUS,t0.CREATED_BY,t0.CREATION_TIME,t0.UPDATED_BY,t0.UPDATION_TIME,t0.BUSINESS_FROM,t0.BUSINESS_TO,t0.PROCESSING_FROM,t0.PROCESSING_TO";
		}

		StringBuffer result = new StringBuffer((databaseAlias.length()+15)*14);
		result.append(databaseAlias).append(".").append("ID");
		result.append(",").append(databaseAlias).append(".").append("SHIFT_ID");
		result.append(",").append(databaseAlias).append(".").append("EMP_ID");
		result.append(",").append(databaseAlias).append(".").append("EXPECTED_START_DATE");
		result.append(",").append(databaseAlias).append(".").append("EXPECTED_END_DATE");
		result.append(",").append(databaseAlias).append(".").append("STATUS");
		result.append(",").append(databaseAlias).append(".").append("CREATED_BY");
		result.append(",").append(databaseAlias).append(".").append("CREATION_TIME");
		result.append(",").append(databaseAlias).append(".").append("UPDATED_BY");
		result.append(",").append(databaseAlias).append(".").append("UPDATION_TIME");
		result.append(",").append(databaseAlias).append(".").append("BUSINESS_FROM");
		result.append(",").append(databaseAlias).append(".").append("BUSINESS_TO");
		result.append(",").append(databaseAlias).append(".").append("PROCESSING_FROM");
		result.append(",").append(databaseAlias).append(".").append("PROCESSING_TO");
		return result.toString();
	}

	public Object getConnectionManager()
	{
		return connectionManager;
	}

	public void setConnectionManager(Object connectionManager, ConnectionManagerWrapper wrapper)
	{
		this.connectionManager = (SourcelessConnectionManager)connectionManager;
		this.connectionManagerWrapper = wrapper;
	}

	public EmpShiftMappingData inflateEmpShiftMappingData(ResultSet rs, DatabaseType dt)
	throws SQLException
	{
		EmpShiftMappingData data = inflateEmpShiftMappingPkData(rs, dt);
		inflateNonPkEmpShiftMappingData(2, data, rs, dt);
		return data;
	}

	public EmpShiftMappingData inflateEmpShiftMappingPkData(ResultSet _rs, DatabaseType _dt)
	throws SQLException
	{
		EmpShiftMappingData _data = new EmpShiftMappingData();
		int _pos = 1;
		_data.setId(_rs.getInt(_pos++));
		checkNullPrimitive(_rs, _data, "id");
		return _data;
	}

	public void inflateNonPkEmpShiftMappingData(int _pos, EmpShiftMappingData _datax, ResultSet _rs, DatabaseType _dt)
	throws SQLException
	{
		{
			EmpShiftMappingData _data = _datax;
			_data.setShiftId(_rs.getInt(_pos++));
			if (_rs.wasNull())
			{
				_data.setShiftIdNull();
			}

			_data.setEmpId(_rs.getInt(_pos++));
			if (_rs.wasNull())
			{
				_data.setEmpIdNull();
			}

			Timestamp expectedStartDatetimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			_data.setExpectedStartDate(expectedStartDatetimestampValue);
			Timestamp expectedEndDatetimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			_data.setExpectedEndDate(expectedEndDatetimestampValue);
			_data.setStatus(trimString(_rs.getString(_pos++)));
			_data.setCreatedBy(trimString(_rs.getString(_pos++)));
			Timestamp creationTimetimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			_data.setCreationTime(creationTimetimestampValue);
			_data.setUpdatedBy(trimString(_rs.getString(_pos++)));
			Timestamp updationTimetimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			_data.setUpdationTime(updationTimetimestampValue);
			Timestamp businessDateFromtimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			_data.setBusinessDateFrom(businessDateFromtimestampValue);
			Timestamp businessDateTotimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			if (businessDateTotimestampValue == null)
			{
				throw new MithraBusinessException("attribute ' businessDateTo ' is null in database but is not marked as nullable in mithra xml for primary key / "+_data.zGetPrintablePrimaryKey());
			}

			businessDateTotimestampValue = MithraTimestamp.zFixInfinity(businessDateTotimestampValue, MithraTimestamp.DefaultTimeZone,
				EmpShiftMappingFinder.businessDate().getInfinityDate());
			_data.setBusinessDateTo(businessDateTotimestampValue);
			Timestamp processingDateFromtimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			_data.setProcessingDateFrom(processingDateFromtimestampValue);
			Timestamp processingDateTotimestampValue = _dt.getTimestampFromResultSet(_rs, _pos++, MithraTimestamp.DefaultTimeZone);
			if (processingDateTotimestampValue == null)
			{
				throw new MithraBusinessException("attribute ' processingDateTo ' is null in database but is not marked as nullable in mithra xml for primary key / "+_data.zGetPrintablePrimaryKey());
			}

			processingDateTotimestampValue = MithraTimestamp.zFixInfinity(processingDateTotimestampValue, MithraTimestamp.DefaultTimeZone,
				EmpShiftMappingFinder.processingDate().getInfinityDate());
			_data.setProcessingDateTo(processingDateTotimestampValue);
		}
	}

	public DatabaseType getDatabaseType()
	{
		return connectionManager.getDatabaseType();
	}

	public TimeZone getDatabaseTimeZone()
	{
		return connectionManager.getDatabaseTimeZone();
	}

	protected String getSchema()
	{
		return this.getSchemaGenericSource(null);
	}

	public String getFullyQualifiedTableName()
	{
		String schema = this.getSchemaGenericSource(null);
		String tableName = getEmpShiftMappingTableName();
		return this.getDatabaseType().getFullyQualifiedTableName(schema, tableName);
	}

	public void setSchemaManager(Object schemaManager)
	{
		if( schemaManager instanceof SchemaManager )
		{
			this.schemaManager = (SchemaManager) schemaManager;
		}
		else
		{
			throw new IllegalArgumentException( "Schema manager class " + schemaManager.getClass().getName()
			+ " does not implement SchemaManager.class" );
		}
	}

	public void setTablePartitionManager(Object tablePartitionManager)
	{
		if( tablePartitionManager instanceof TablePartitionManager )
		{
			this.tablePartitionManager = (TablePartitionManager) tablePartitionManager;
		}
		else
		{
			throw new IllegalArgumentException( "Table partition manager class " + tablePartitionManager.getClass().getName()
			+ " does not implement TablePartitionManager.class" );
		}
	}

	public String getTableName()
	{
		return this.getDefaultTableName();
	}

	public String getDefaultTableName()
	{
		return "EMP_SHIFT_MAPPING";
	}

	public Timestamp[] getAsOfDates() 
	{
		return new Timestamp[2];
	}

	public MithraDatedObject createObject(MithraDataObject data, Timestamp[] asOfDates)
	{
		EmpShiftMapping newObject = new EmpShiftMapping(asOfDates[0]
		, asOfDates[1]
		);
		newObject.zSetFromEmpShiftMappingData((EmpShiftMappingData) data);
		return newObject;
	}

	public String getAsOfAttributeWhereSql(MithraDataObject data) 
	{
		String result = "";
		result += " AND ";
		result +="BUSINESS_TO = ?";
		result += " AND ";
		result +="PROCESSING_TO = ?";
		return result;
	}

	public int setPrimaryKeyAttributesWithoutDates(PreparedStatement stm, int pos, MithraDataObject dataObject, TimeZone databaseTimeZone, DatabaseType dt) throws SQLException
	{
		EmpShiftMappingData data = (EmpShiftMappingData)dataObject;
		TimeZone conversionTimeZone = null;
		stm.setInt(pos++, data.getId());
		return pos;
	}
}
